<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import {
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
        Tooltip,
    } from 'flowbite-svelte';
    
    import Pagination from '$src/lib/components/UI/pagination.svelte';

    import { 
        displayDate, 
        timeAgo, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon,
        getStatusBadgeConfig,
        getPriorityBadgeConfig
    } from '$lib/utils';
    
    import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';


    export let customer_tickets: any;

    $: sortedTickets = customer_tickets;

    //////////////// Pagination Logic ////////////////
    // pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;
    
    // Loading state for ticket navigation
    let loadingTicketId: number | null = null;

    $: totalPages = Math.ceil(Math.max((sortedTickets.tickets ?? []).length, 1) / itemsPerPage);
    $: paginatedDocuments = (sortedTickets.tickets ?? []).slice(0, itemsPerPage);

    function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedDocuments = sortedTickets.tickets.slice(idx, Math.min(idx + itemsPerPage, sortedTickets.tickets.length));
	}
    
    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;        
        updatePagination();
    }

    function getOverdueTickets(tickets: any[]) {
        const now = new Date();

        return tickets.filter((ticket: any) => {
            const createdAt = new Date(ticket.created_at);
            const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

            return hoursDiff > 8 && ticket.status.toLowerCase() !== 'close';
        }).length;
    }

    // Ticket navigation with loading state
    async function handleTicketClick(ticketId: number) {
        if (loadingTicketId !== null) return; // Prevent multiple clicks
        
        loadingTicketId = ticketId;
        
        // Small delay to prevent flashing on fast navigations
        await new Promise(resolve => setTimeout(resolve, 100));
        
        try {
            window.location.href = `/monitoring/${ticketId}`;
        } catch (error) {
            console.error('Navigation failed:', error);
            loadingTicketId = null;
        }
    }
</script>

<!-- Ticket details -->
<!-- <div class="mb-4 flex items-center justify-start">
    <TicketSolid class="text-black-500 h-5 w-5" />
    <h3 class="mb-0 ml-2 text-xl font-semibold">Ticket Information</h3>
</div> -->

<!--Summary Cards-->
<!-- <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('tickets')}</p>
            <p class="text-2xl font-bold">{customer_tickets.tickets.length}</p>
        </div>
    </div>

    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('overdue_ticket')}</p>
            <p class="text-2xl font-bold">{getOverdueTickets(customer_tickets.tickets)}</p>
        </div>
    </div>
</div> -->

{#if sortedTickets.length === 0}
    <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
        <div class="p-6 text-center text-gray-500">
            {t('no_tickets_found')}
        </div>
    </div>
{:else}
    <div class="mb-6 w-full">
        <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
            <div class="w-full">
                {#if sortedTickets.tickets && sortedTickets.tickets.length > 0}
                    <div class="h-full max-h-[calc(100vh-20rem)] overflow-y-auto">
                        <Table> 
                            <TableHead>
                                <TableHeadCell class="w-16 px-2 text-center align-middle">{t('table_no')}</TableHeadCell>
                                <TableHeadCell class="w-16 px-2 text-center align-middle">{t('table_status')}</TableHeadCell>
                                <TableHeadCell class="w-16 px-2 text-center align-middle">{t('table_priority')}</TableHeadCell>
                                <TableHeadCell class="w-16 px-2 text-center align-middle">{t('table_sentiment')}</TableHeadCell>
                                <TableHeadCell class="w-40 px-2 text-left align-middle">{t('table_agent')}</TableHeadCell>
                                <TableHeadCell class="w-32 px-2 text-left align-middle">{t('table_time')}</TableHeadCell>
                                <TableHeadCell class="w-32 px-2 text-left align-middle">{t('table_updated_on')}</TableHeadCell>
                            </TableHead>
                            <TableBody>
                                {#each paginatedDocuments as ticket}
                                <TableBodyRow 
                                    class="cursor-pointer hover:bg-gray-50 transition-colors duration-150"
                                    on:click={() => handleTicketClick(ticket.id)}
                                    role="button"
                                    aria-label={loadingTicketId === ticket.id ? 'Loading ticket details' : 'View ticket details'}
                                    aria-busy={loadingTicketId === ticket.id ? 'true' : 'false'}
                                >
                                    <TableBodyCell class="w-16 justify-center px-2 text-center align-middle">
                                        {#if loadingTicketId === ticket.id}
                                            <div class="flex justify-center items-center" aria-label="Loading ticket details">
                                                <LoadingSpinner size="sm" color="blue" />
                                            </div>
                                        {:else}
                                            <span class="text-gray-900 text-xs">
                                                {ticket.id}
                                            </span>
                                        {/if}
                                    </TableBodyCell>
                                    <TableBodyCell class="w-16 justify-center px-2 text-center align-middle">
                                        <div class="flex justify-start">
                                            <span class={`${getStatusClass(ticket.status_id)} w-20 rounded-md px-2 text-center text-xs`}>
                                                {getStatusBadgeConfig(ticket.status_id, ticket.status).text}
                                            </span>
                                        </div>
                                    </TableBodyCell>
                                    
                                    <TableBodyCell class="w-16 px-2 text-center align-middle">
                                        <div class="flex justify-start">  
                                            <span class={`${getPriorityClass(ticket.priority.name)} w-20 rounded-md px-2 text-xs`}>
                                                {getPriorityBadgeConfig(ticket.priority.name).text}
                                            </span>
                                        </div>                        
                                    </TableBodyCell>
                                    
                                    <TableBodyCell class="w-16 px-2 text-center align-middle">
                                        <div class="flex justify-center"> 
                                            <div class={`flex items-center justify-center gap-1 rounded-md ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
                                                <img
                                                    src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
                                                    alt={ticket.latest_analysis?.sentiment}
                                                    class="h-5 w-5"
                                                />
                                                <Tooltip class="text-xs">{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
                                            </div>
                                        </div>
                                    </TableBodyCell>
                                    
                                    <TableBodyCell class="w-40 px-2 text-left text-xs align-middle break-words whitespace-normal">
                                        {ticket.owner.name ? ticket.owner.name : '-'}
                                    </TableBodyCell>

                                    <TableBodyCell class="w-32 px-2 text-left text-xs align-middle">
                                        {timeAgo(ticket.updated_on, ticket.status)}
                                    </TableBodyCell>

                                    <TableBodyCell class="w-32 px-2 text-left text-xs align-middle">
                                        <div>{displayDate(ticket.updated_on).date}</div>
                                        <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
                                    </TableBodyCell>
                                </TableBodyRow>
                                {/each}
                            </TableBody>
                        </Table>
                    </div>
                {:else}
                    <div class="p-6 text-center text-gray-500">
                        {t('no_tickets_found')}
                    </div>
                {/if}
            </div>
        </div>
        
        <!-- Pagination outside the scrollable area -->
        <div class="mt-4">
            <Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
        </div>
    </div>
{/if}

<style>
    /* Custom Scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 8px; /* Thinner scrollbar */
    }
    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1; /* Light track color */
        border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #888; /* Thumb color */
        border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #555; /* Thumb color on hover */
    }
</style>