import type { 
    PollingEndpoint, 
    PollingConfig, 
    PollingState, 
    PollingServiceEvents 
} from '../types/polling';

/**
 * Shared Polling Service
 * 
 * Unified polling system with optimized performance patterns:
 * - 5-second default intervals
 * - Hash-based change detection
 * - Tab visibility handling
 * - Memory-optimized snapshots
 * - Force refresh timing
 */
export class PollingService {
    private static instance: PollingService;
    private state: PollingState;
    private events: PollingServiceEvents;
    private userInteractionTimeout: ReturnType<typeof setTimeout> | null = null;

    private constructor() {
        this.state = {
            isTabVisible: true,
            isPaused: false,
            isGloballyEnabled: true,
            debugMode: false,
            activeEndpoints: new Map()
        };
        
        this.events = {};
        this.initializeEventListeners();
    }

    public static getInstance(): PollingService {
        if (!PollingService.instance) {
            PollingService.instance = new PollingService();
        }
        return PollingService.instance;
    }

    /**
     * Register a new polling endpoint
     */
    public registerEndpoint(id: string, config: PollingConfig): boolean {
        try {
            if (this.state.activeEndpoints.has(id)) {
                if (this.state.debugMode) {
                    console.warn(`PollingService: Endpoint '${id}' already exists, updating configuration`);
                }
                this.unregisterEndpoint(id);
            }

            const endpoint: PollingEndpoint = {
                id,
                url: config.endpoint || 'custom',
                interval: config.interval || 5000, // Default 5 seconds
                enabled: true,
                lastFetch: new Date(),
                lastForceRefresh: new Date(),
                pollingIntervalId: null,
                snapshot: new Map(),
                changeDetectionFn: config.changeDetectionFn || this.defaultChangeDetection,
                onDataChange: config.onDataChange,
                onError: config.onError,
                customFetcher: config.customFetcher
            };

            this.state.activeEndpoints.set(id, endpoint);
            
            if (this.state.isGloballyEnabled) {
                this.startEndpointPolling(id);
            }

            if (this.state.debugMode) {
                console.log(`PollingService: Registered endpoint '${id}' with ${endpoint.interval}ms interval`);
            }

            return true;
        } catch (error) {
            console.error(`PollingService: Failed to register endpoint '${id}':`, error);
            return false;
        }
    }

    /**
     * Unregister and cleanup polling endpoint
     */
    public unregisterEndpoint(id: string): boolean {
        try {
            const endpoint = this.state.activeEndpoints.get(id);
            if (!endpoint) {
                if (this.state.debugMode) {
                    console.warn(`PollingService: Endpoint '${id}' not found for unregistration`);
                }
                return false;
            }

            this.stopEndpointPolling(id);
            endpoint.snapshot.clear();
            this.state.activeEndpoints.delete(id);

            if (this.state.debugMode) {
                console.log(`PollingService: Unregistered endpoint '${id}'`);
            }

            return true;
        } catch (error) {
            console.error(`PollingService: Failed to unregister endpoint '${id}':`, error);
            return false;
        }
    }

    /**
     * Start polling for specific endpoint
     */
    public startEndpointPolling(id: string): boolean {
        try {
            const endpoint = this.state.activeEndpoints.get(id);
            if (!endpoint) {
                console.warn(`PollingService: Cannot start polling for unknown endpoint '${id}'`);
                return false;
            }

            if (endpoint.pollingIntervalId) {
                clearInterval(endpoint.pollingIntervalId);
            }

            if (this.state.isGloballyEnabled && this.state.isTabVisible && !this.state.isPaused && endpoint.enabled) {
                endpoint.pollingIntervalId = setInterval(() => {
                    if (!this.state.isPaused && endpoint.enabled) {
                        const shouldForceRefresh = Date.now() - endpoint.lastForceRefresh.getTime() > 30000;
                        if (shouldForceRefresh) {
                            if (this.state.debugMode) {
                                console.log(`PollingService: Force refresh triggered for '${id}'`);
                            }
                            endpoint.lastForceRefresh = new Date();
                        }
                        this.fetchEndpointData(id, shouldForceRefresh);
                    }
                }, endpoint.interval);

                if (this.state.debugMode) {
                    console.log(`PollingService: Started polling for '${id}' with ${endpoint.interval}ms interval`);
                }
            }

            return true;
        } catch (error) {
            console.error(`PollingService: Failed to start polling for '${id}':`, error);
            return false;
        }
    }

    /**
     * Stop polling for specific endpoint
     */
    public stopEndpointPolling(id: string): boolean {
        try {
            const endpoint = this.state.activeEndpoints.get(id);
            if (!endpoint) {
                return false;
            }

            if (endpoint.pollingIntervalId) {
                clearInterval(endpoint.pollingIntervalId);
                endpoint.pollingIntervalId = null;
            }

            if (this.state.debugMode) {
                console.log(`PollingService: Stopped polling for '${id}'`);
            }

            return true;
        } catch (error) {
            console.error(`PollingService: Failed to stop polling for '${id}':`, error);
            return false;
        }
    }

    /**
     * Enable/disable polling for specific endpoint
     */
    public setEndpointEnabled(id: string, enabled: boolean): boolean {
        try {
            const endpoint = this.state.activeEndpoints.get(id);
            if (!endpoint) {
                return false;
            }

            endpoint.enabled = enabled;

            if (enabled) {
                this.startEndpointPolling(id);
            } else {
                this.stopEndpointPolling(id);
            }

            if (this.state.debugMode) {
                console.log(`PollingService: ${enabled ? 'Enabled' : 'Disabled'} endpoint '${id}'`);
            }

            return true;
        } catch (error) {
            console.error(`PollingService: Failed to set endpoint '${id}' enabled state:`, error);
            return false;
        }
    }

    /**
     * Global pause/resume all polling
     */
    public setPaused(paused: boolean): void {
        this.state.isPaused = paused;
        
        if (paused) {
            this.stopAllPolling();
        } else {
            this.startAllPolling();
        }

        if (this.state.debugMode) {
            console.log(`PollingService: ${paused ? 'Paused' : 'Resumed'} all polling`);
        }
    }

    /**
     * Enable/disable debug mode
     */
    public setDebugMode(enabled: boolean): void {
        this.state.debugMode = enabled;
        console.log(`PollingService: Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get current polling state
     */
    public getState(): PollingState {
        return { ...this.state };
    }

    /**
     * Get endpoint info
     */
    public getEndpoint(id: string): PollingEndpoint | null {
        return this.state.activeEndpoints.get(id) || null;
    }

    /**
     * Cleanup all polling
     */
    public cleanup(): void {
        try {
            // Stop all polling
            this.stopAllPolling();
            
            // Clear all endpoints
            for (const [id] of this.state.activeEndpoints) {
                this.unregisterEndpoint(id);
            }

            // Clear timeouts
            if (this.userInteractionTimeout) {
                clearTimeout(this.userInteractionTimeout);
                this.userInteractionTimeout = null;
            }

            // Remove event listeners
            this.removeEventListeners();

            if (this.state.debugMode) {
                console.log('PollingService: Cleanup completed');
            }
        } catch (error) {
            console.error('PollingService: Error during cleanup:', error);
        }
    }

    /**
     * Default change detection using hash comparison
     */
    private defaultChangeDetection = (oldData: any, newData: any): boolean => {
        try {
            if (!oldData || !newData) return true;
            
            // Quick length check for arrays
            if (Array.isArray(oldData) && Array.isArray(newData)) {
                if (oldData.length !== newData.length) return true;
                
                // Create lightweight hash of essential data
                const oldHash = oldData.map(item => 
                    typeof item === 'object' ? JSON.stringify(item) : String(item)
                ).join('|');
                
                const newHash = newData.map(item => 
                    typeof item === 'object' ? JSON.stringify(item) : String(item)
                ).join('|');
                
                return oldHash !== newHash;
            }
            
            // Object comparison
            return JSON.stringify(oldData) !== JSON.stringify(newData);
        } catch (error) {
            console.error('PollingService: Error in change detection:', error);
            return true; // Assume changes if detection fails
        }
    };

    /**
     * Fetch data for specific endpoint
     */
    private async fetchEndpointData(id: string, forceRefresh: boolean = false): Promise<void> {
        const endpoint = this.state.activeEndpoints.get(id);
        if (!endpoint) return;

        try {
            let newData: any;
            
            if (endpoint.customFetcher) {
                // Use custom fetcher function
                if (this.state.debugMode) {
                    console.log(`PollingService: Using custom fetcher for '${id}'`);
                }
                newData = await endpoint.customFetcher();
            } else {
                // Use HTTP endpoint
                const queryParam = forceRefresh ? `?t=${Date.now()}` : '';
                const response = await fetch(`${endpoint.url}${queryParam}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                newData = await response.json();
            }
            
            endpoint.lastFetch = new Date();

            // Check for changes using endpoint's change detection function
            const hasChanges = endpoint.changeDetectionFn!(endpoint.snapshot.get('data'), newData);

            if (hasChanges || forceRefresh) {
                // Update snapshot
                endpoint.snapshot.set('data', newData);
                endpoint.snapshot.set('lastUpdate', new Date());

                // Notify change callback
                if (endpoint.onDataChange) {
                    endpoint.onDataChange(newData, { hasChanges, forceRefresh });
                }

                // Notify global event listeners
                if (this.events.onEndpointUpdate) {
                    this.events.onEndpointUpdate(id, newData);
                }

                if (this.state.debugMode) {
                    console.log(`PollingService: Data updated for '${id}'`, { hasChanges, forceRefresh });
                }
            } else if (this.state.debugMode) {
                console.log(`PollingService: No changes detected for '${id}'`);
            }

        } catch (error) {
            console.error(`PollingService: Error fetching data for '${id}':`, error);
            
            if (endpoint.onError) {
                endpoint.onError(error as Error);
            }
            
            if (this.events.onError) {
                this.events.onError(id, error as Error);
            }
        }
    }

    /**
     * Start all enabled endpoints
     */
    private startAllPolling(): void {
        for (const [id, endpoint] of this.state.activeEndpoints) {
            if (endpoint.enabled) {
                this.startEndpointPolling(id);
            }
        }
    }

    /**
     * Stop all endpoints
     */
    private stopAllPolling(): void {
        for (const [id] of this.state.activeEndpoints) {
            this.stopEndpointPolling(id);
        }
    }

    /**
     * Handle tab visibility changes
     */
    private handleVisibilityChange = (): void => {
        this.state.isTabVisible = !document.hidden;
        
        if (this.state.isTabVisible) {
            if (this.state.debugMode) {
                console.log('PollingService: Tab became visible, resuming polling');
            }
            this.startAllPolling();
        } else {
            if (this.state.debugMode) {
                console.log('PollingService: Tab became hidden, pausing polling');
            }
            this.stopAllPolling();
        }
    };

    /**
     * Handle user interactions - temporarily pause polling
     */
    private pausePollingTemporarily = (): void => {
        if (this.userInteractionTimeout) {
            clearTimeout(this.userInteractionTimeout);
        }

        this.setPaused(true);

        this.userInteractionTimeout = setTimeout(() => {
            this.setPaused(false);
        }, 2000); // Resume after 2 seconds of inactivity
    };

    /**
     * Initialize event listeners
     */
    private initializeEventListeners(): void {
        if (typeof document !== 'undefined') {
            document.addEventListener('visibilitychange', this.handleVisibilityChange);
            
            // User interaction events to temporarily pause polling
            ['click', 'scroll', 'keypress', 'touchstart'].forEach(eventType => {
                document.addEventListener(eventType, this.pausePollingTemporarily, { passive: true });
            });
        }
    }

    /**
     * Remove event listeners
     */
    private removeEventListeners(): void {
        if (typeof document !== 'undefined') {
            document.removeEventListener('visibilitychange', this.handleVisibilityChange);
            
            ['click', 'scroll', 'keypress', 'touchstart'].forEach(eventType => {
                document.removeEventListener(eventType, this.pausePollingTemporarily);
            });
        }
    }

    /**
     * Set global event handlers
     */
    public setEventHandlers(events: PollingServiceEvents): void {
        this.events = { ...events };
    }
}