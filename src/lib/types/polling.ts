// Polling service type definitions

export interface PollingEndpoint {
    id: string;
    url: string;
    interval: number;
    enabled: boolean;
    lastFetch: Date;
    lastForceRefresh: Date;
    pollingIntervalId: number | null;
    snapshot: Map<string, any>;
    changeDetectionFn?: (oldData: any, newData: any) => boolean;
    onDataChange?: (data: any, changes: any) => void;
    onError?: (error: Error) => void;
    customFetcher?: () => Promise<any>;
}

export interface PollingConfig {
    endpoint?: string;
    interval?: number;
    forceRefreshInterval?: number;
    changeDetectionFn?: (oldData: any, newData: any) => boolean;
    onDataChange?: (data: any, changes: any) => void;
    onError?: (error: Error) => void;
    debugMode?: boolean;
    customFetcher?: () => Promise<any>;
}

export interface PollingState {
    isTabVisible: boolean;
    isPaused: boolean;
    isGloballyEnabled: boolean;
    debugMode: boolean;
    activeEndpoints: Map<string, PollingEndpoint>;
}

export interface PollingServiceEvents {
    onStateChange?: (state: PollingState) => void;
    onEndpointUpdate?: (endpointId: string, data: any) => void;
    onError?: (endpointId: string, error: Error) => void;
}

export interface StatusUpdateResponse {
    id: number;
    status: string;
    last_active: string;
    is_active: boolean;
    current_workload?: number;
}

export interface SidebarStatusData {
    user_id: number;
    fullname: string;
    work_email: string;
    status: string;
    last_active: string;
    is_active: boolean;
    checksum?: string;
}